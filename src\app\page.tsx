'use client'

import React, { useState, useEffect } from 'react'
import LoadingScreen from '@/pages/LoadingScreen'
import HomeScreen from '@/pages/HomeScreen'
import GameScreen from '@/pages/GameScreen'
import { useLanguageStore } from '@/stores/useLanguageStore'
import { useNativeToken } from '@/hooks/useNativeToken'
import { useAudioStore } from '@/stores/useAudioStore'
import Image from 'next/image'
import { PostMessageType } from '@/lib/postMessage'

declare global {
  interface Window {
    ReactNativeWebView?: {
      postMessage: (message: string) => void
    }
  }
}

export default function Page() {
  const [loaded, setLoaded] = useState(false)
  const [error, setError] = useState(false)
  const [selectedStage, setSelectedStage] = useState<number | null>(null)
  const setLanguage = useLanguageStore((state) => state.setLanguage)
  const { startBackgroundMusic, pauseBackgroundMusic, resumeBackgroundMusic, init } = useAudioStore()

  // Initialize native token listener at app level so it persists throughout the app lifecycle
  const { isLoading: tokenLoading, hasToken, hasTimedOut } = useNativeToken(5000)

  const handleGoBack = () => {
    try {
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(
          JSON.stringify({
            type: PostMessageType.GO_BACK
          })
        )
      }
    } catch (error) {
      console.error('Failed to send go back message to native app:', error)
    }
  }

  useEffect(() => {
    setLanguage('VN')

    // Initialize audio system
    init()

    // Set up global window functions for native app - available throughout app lifecycle
    window.startBackgroundMusic = () => {
      console.log('Native app called startBackgroundMusic')
      startBackgroundMusic()
    }
    window.pauseBackgroundMusic = () => {
      console.log('Native app called pauseBackgroundMusic')
      pauseBackgroundMusic()
    }
    window.resumeBackgroundMusic = () => {
      console.log('Native app called resumeBackgroundMusic')
      resumeBackgroundMusic()
    }

    // Debug function for native app to check music state
    window.checkMusicState = () => {
      const state = useAudioStore.getState()
      console.log('Current music state:', {
        isMuted: state.isMuted,
        isBackgroundMusicPlaying: state.isBackgroundMusicPlaying,
        hasUserInteracted: state.hasUserInteracted
      })
      return state
    }
  }, [setLanguage, init, startBackgroundMusic, pauseBackgroundMusic, resumeBackgroundMusic])

  // Handle token timeout - redirect to error screen
  if (hasTimedOut || error) {
    return (
      <div className='font-montserrat flex h-dvh w-dvw items-center justify-center'>
        <div className='flex h-[42.6%] w-[89.3%] flex-col'>
          <div className='flex h-[40.6%] w-full items-start justify-center'>
            <Image src='/loading/404.png' alt='404 Error' className='h-full w-auto' width={183} height={128} />
          </div>
          <div className='flex h-[17.1%] w-full items-center justify-center text-[1.5rem] font-bold'>
            Đã có lỗi xảy ra
          </div>
          <div className='flex h-[15.3%] w-full items-center justify-center px-2 text-center text-[0.9rem] text-[#636F81]'>
            Hãy đặt dịch vụ ngay để trải nghiệm những tiện ích gia đình tốt nhất của bTaskee nhé!
          </div>
          <div className='relative h-[27%] w-full'>
            <div
              className='absolute bottom-0 left-1/2 flex h-[53.5%] w-[47.4%] -translate-x-1/2 transform items-center justify-center rounded-[0.625rem] bg-[#1BB55C] text-[0.95rem] font-semibold text-white'
              onClick={() => {
                handleGoBack()
              }}
            >
              Quay về bTaskee
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (selectedStage !== null) {
    return <GameScreen stage={selectedStage} onBack={() => setSelectedStage(null)} />
  }

  // Show loading screen until token is received AND game data is loaded
  const shouldShowLoading = tokenLoading || !hasToken || !loaded

  return shouldShowLoading ? (
    <LoadingScreen
      onSuccess={() => setLoaded(true)}
      onError={() => setError(true)}
      tokenLoading={tokenLoading}
      hasToken={hasToken}
    />
  ) : (
    <HomeScreen onStageClick={(stage: number) => setSelectedStage(stage)} />
  )
}
